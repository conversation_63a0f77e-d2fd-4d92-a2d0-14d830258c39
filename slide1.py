from manim import *
import numpy as np

class DrugDiscoveryPresentation(Scene):
    def construct(self):
        # Set the background color to 3B1B style dark blue
        self.camera.background_color = "#0f0f23"
        
        # Title sequence
        self.title_sequence()
        self.wait(1)
        
        # Main content sections
        self.exponential_growth_visual()
        self.wait(1)
        
        self.rd_expenditure_section()
        self.wait(1)
        
        self.ai_adoption_section()
        self.wait(1)
        
        self.speed_cost_section()
        self.wait(1)
        
        self.complexity_section()
        self.wait(1)
        
        # Conclusion
        self.conclusion_sequence()

    def title_sequence(self):
        # Create main title with elegant typography
        title = Text(
            "The Exponential Growth of\nComputational Drug Discovery",
            font_size=48,
            color=WHITE,
            font="Consolas"
        ).to_edge(UP, buff=1)
        
        # Subtitle
        subtitle = Text(
            "A Data-Driven Transformation in Pharmaceuticals",
            font_size=28,
            color=BLUE_C,
            font="Consolas"
        ).next_to(title, DOWN, buff=0.5)
        
        # Animate title entrance
        self.play(
            Write(title, run_time=1.5),
            Write(subtitle, run_time=1.0),
            lag_ratio=0.1
        )
        
        self.wait(2)
        
        # Clear the screen
        self.play(
            FadeOut(title),
            FadeOut(subtitle)
        )

    def exponential_growth_visual(self):
        # Title for this section
        section_title = Text(
            "Exponential Growth Pattern",
            font_size=36,
            color=YELLOW,
            font="Consolas"
        ).to_edge(UP)

        # Create exponential growth curve
        axes = Axes(
            x_range=[2015, 2030, 5],
            y_range=[0, 100, 20],
            x_length=10,
            y_length=6,
            axis_config={"color": BLUE_C},
            x_axis_config={"numbers_to_include": [2015, 2020, 2025, 2030]},
            y_axis_config={"numbers_to_include": [0, 20, 40, 60, 80, 100]}
        ).center() # Center the axes for better composition
        
        # Labels
        x_label = Text("Year", font_size=24, color=WHITE).next_to(axes.x_axis, DOWN)
        y_label = Text("Market Size (Billion $)", font_size=24, color=WHITE).next_to(axes.y_axis, LEFT).rotate(PI/2)
        
        # Exponential curve
        curve = axes.plot(
            lambda x: 5 * np.exp(0.15 * (x - 2015)),
            x_range=[2015, 2030],
            color=YELLOW
        )
        
        # Data points (reduced for simplicity, still showing trend)
        points_data = [
            (2015, 5), (2020, 12), (2025, 28), (2030, 75)
        ]
        
        dots = VGroup()
        for year, value in points_data:
            dot = Dot(axes.coords_to_point(year, value), color=RED, radius=0.08)
            dots.add(dot)
        
        # Animate the growth visualization
        self.play(Write(section_title))
        self.play(Create(axes), Write(x_label), Write(y_label))
        
        # Draw curve progressively
        self.play(Create(curve, run_time=2))
        
        # Add data points
        self.play(LaggedStart(*[Create(dot) for dot in dots], lag_ratio=0.3))
        
        # Add growth rate annotation
        growth_text = Text(
            "~15% Annual Growth Rate",
            font_size=24,
            color=GREEN
        ).next_to(curve.get_top(), UP + RIGHT, buff=0.5)
        
        arrow = Arrow(
            growth_text.get_bottom(),
            curve.point_from_proportion(0.8), # Point to a relevant part of the curve
            color=GREEN,
            buff=0.1
        )
        
        self.play(Write(growth_text), Create(arrow))
        self.wait(2.5)
        
        # Clear for next section
        self.play(FadeOut(VGroup(section_title, axes, x_label, y_label, curve, dots, growth_text, arrow)))

    def rd_expenditure_section(self):
        # Section title
        title = Text(
            "1. Increasing R&D Expenditure",
            font_size=36,
            color=BLUE_B,
            font="Consolas"
        ).to_edge(UP)
        
        # Create bar chart for R&D spending
        bars_data = [
            ("2018", 180, BLUE_C),
            ("2020", 200, BLUE_B),
            ("2022", 230, BLUE_A),
            ("2024", 260, WHITE)
        ]
        
        bars = VGroup()
        labels = VGroup()
        
        # Calculate max height to scale bars
        max_val = max([d[1] for d in bars_data])
        scale_factor = 4 / max_val # Max bar height of 4 units
        
        for i, (year, value, color) in enumerate(bars_data):
            bar = Rectangle(
                width=1.2,
                height=value * scale_factor,
                color=color,
                fill_opacity=0.8
            ).move_to(LEFT * 4 + RIGHT * i * 2.5, aligned_edge=DOWN) # Align bars to bottom
            
            # Value label on top
            value_label = Text(f"${value}B", font_size=20, color=WHITE).next_to(bar, UP, buff=0.1)
            
            # Year label below
            year_label = Text(year, font_size=18, color=WHITE).next_to(bar, DOWN, buff=0.3)
            
            bars.add(bar)
            labels.add(value_label)
            labels.add(year_label)
        
        bars.center().shift(DOWN*0.5) # Center the bar group
        labels.center().shift(DOWN*0.5)
        
        # Key insights
        insights = VGroup(
            Text("• Global pharma R&D spending continues to rise.", font_size=24, color=WHITE),
            Text("• ~44% increase over 6 years (2018-2024).", font_size=24, color=YELLOW),
            Text("• Computational methods offer cost reduction.", font_size=24, color=GREEN),
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.4).to_edge(RIGHT, buff=1.5).shift(UP*1)
        
        # Animate
        self.play(Write(title))
        self.play(LaggedStart(*[FadeIn(bar) for bar in bars], lag_ratio=0.3))
        self.play(LaggedStart(*[Write(label) for label in labels], lag_ratio=0.1))
        self.play(LaggedStart(*[Write(insight) for insight in insights], lag_ratio=0.5))
        
        self.wait(3)
        self.play(FadeOut(VGroup(title, bars, labels, insights)))

    def ai_adoption_section(self):
        title = Text(
            "2. AI & Machine Learning Adoption",
            font_size=36,
            color=GREEN_B,
            font="Consolas"
        ).to_edge(UP)
        
        # AI adoption statistics
        stats = VGroup(
            Text("AI in Drug Discovery Trends:", font_size=28, color=WHITE, weight=BOLD),
            Text("", font_size=20),  # spacer
            Text("• 2019: 12% of companies using AI.", font_size=24, color=LIGHT_GRAY),
            Text("• 2021: 38% of companies using AI.", font_size=24, color=YELLOW),
            Text("• 2024: 67% of companies using AI.", font_size=24, color=GREEN),
            Text("", font_size=20),  # spacer
            Text("Projected by 2030:", font_size=24, color=WHITE, weight=BOLD),
            Text("~85% adoption rate expected.", font_size=28, color=BLUE_B),
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.3).center()
        
        # Simple AI icon (abstract)
        ai_icon = VGroup(
            Circle(radius=0.7, color=BLUE_C, fill_opacity=0.3),
            Dot(color=YELLOW, radius=0.1).shift(UP*0.3 + LEFT*0.3),
            Dot(color=YELLOW, radius=0.1).shift(UP*0.3 + RIGHT*0.3),
            Dot(color=YELLOW, radius=0.1).shift(DOWN*0.3),
            Line(start=UP*0.3 + LEFT*0.3, end=DOWN*0.3, color=WHITE, stroke_width=2),
            Line(start=UP*0.3 + RIGHT*0.3, end=DOWN*0.3, color=WHITE, stroke_width=2),
            Text("AI", font_size=24, color=WHITE, weight=BOLD).scale(0.8)
        ).scale(0.8).to_edge(LEFT, buff=1.5).shift(DOWN*0.5)

        self.play(Write(title))
        self.play(FadeIn(ai_icon)) # Simple fade in for icon
        self.play(LaggedStart(*[Write(stat) for stat in stats], lag_ratio=0.3))
        
        self.wait(3)
        self.play(FadeOut(VGroup(title, ai_icon, stats)))

    def speed_cost_section(self):
        title = Text(
            "3. Need for Speed & Cost Efficiency",
            font_size=36,
            color=RED_B,
            font="Consolas"
        ).to_edge(UP)
        
        # Traditional vs Computational comparison
        traditional_side = VGroup(
            Text("Traditional Drug Discovery", font_size=28, color=RED_C, weight=BOLD),
            Text("", font_size=16),  # spacer
            Text("⏱️ 10-15 years", font_size=24, color=WHITE),
            Text("💰 ~$2.6 billion", font_size=24, color=WHITE),
            Text("📉 ~90% failure rate", font_size=24, color=WHITE),
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.4).to_edge(LEFT, buff=1)
        
        vs_text = Text("VS", font_size=48, color=YELLOW, weight=BOLD).next_to(traditional_side, RIGHT, buff=1.5)
        
        computational_side = VGroup(
            Text("Computational Methods", font_size=28, color=GREEN_C, weight=BOLD),
            Text("", font_size=16),  # spacer
            Text("⚡ 3-7 years", font_size=24, color=GREEN),
            Text("💸 $500M - $1B", font_size=24, color=GREEN),
            Text("📈 ~70% success rate", font_size=24, color=GREEN),
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.4).next_to(vs_text, RIGHT, buff=1.5)
        
        # Position the comparison block
        comparison_group = VGroup(traditional_side, vs_text, computational_side).center().shift(UP*0.5)

        # Speed visualization (simple arrow to emphasize direction)
        efficiency_line = Line(LEFT*5, RIGHT*5, color=BLUE, stroke_width=5)
        efficiency_arrow = Arrow(
            start=efficiency_line.get_left(),
            end=efficiency_line.get_right(),
            color=BLUE,
            stroke_width=5,
            tip_length=0.3
        ).shift(DOWN * 2.5)
        
        speed_text = Text("Significantly Faster & Cheaper", font_size=28, color=BLUE).next_to(efficiency_arrow, UP, buff=0.5)
        
        self.play(Write(title))
        self.play(
            LaggedStart(
                Write(traditional_side),
                Write(vs_text),
                Write(computational_side),
                lag_ratio=0.3
            )
        )
        self.play(GrowArrow(efficiency_arrow), Write(speed_text))
        
        self.wait(3)
        self.play(FadeOut(VGroup(title, comparison_group, efficiency_arrow, speed_text)))

    def complexity_section(self):
        title = Text(
            "4. Growing Target Complexity",
            font_size=36,
            color=PURPLE_B,
            font="Consolas"
        ).to_edge(UP)
        
        # Complexity indicators (text-focused)
        complexity_info = VGroup(
            Text("Modern Drug Targets are Complex:", font_size=28, color=WHITE, weight=BOLD),
            Text("", font_size=16),
            Text("🧬 Protein-protein interactions", font_size=22, color=LIGHT_GRAY),
            Text("🔬 Membrane proteins & pathways", font_size=22, color=LIGHT_GRAY),
            Text("🧠 CNS disorders & multifactorial diseases", font_size=22, color=LIGHT_GRAY),
            Text("🎯 Rare diseases with unique targets", font_size=22, color=LIGHT_GRAY),
            Text("", font_size=16),
            Text("Computational Advantages:", font_size=24, color=YELLOW, weight=BOLD),
            Text("• Molecular dynamics simulations for interactions", font_size=20, color=GREEN),
            Text("• Structure-based drug design for precision", font_size=20, color=GREEN),
            Text("• AI-powered target prediction & validation", font_size=20, color=GREEN),
        ).arrange(DOWN, aligned_edge=LEFT, buff=0.3).to_edge(LEFT, buff=1.5).shift(UP*0.5)
        
        # Complexity growth visualization (simple curve)
        axes = Axes(
            x_range=[0, 10, 2],
            y_range=[0, 10, 2],
            x_length=5,
            y_length=4,
            axis_config={"color": GRAY}
        ).to_edge(RIGHT, buff=1.5).shift(DOWN*0.5)
        
        # Exponential complexity curve
        curve = axes.plot(
            lambda x: x**1.5,
            x_range=[0, 8],
            color=PURPLE_B
        )
        
        chart_title = Text("Target Complexity Over Time", font_size=22, color=WHITE).next_to(axes, UP, buff=0.5)
        x_label = Text("Progress", font_size=18, color=WHITE).next_to(axes.x_axis, DOWN)
        y_label = Text("Complexity", font_size=18, color=WHITE).next_to(axes.y_axis, LEFT).rotate(PI/2)

        self.play(Write(title))
        self.play(LaggedStart(*[Write(info) for info in complexity_info], lag_ratio=0.2))
        self.play(Create(axes), Write(chart_title), Write(x_label), Write(y_label))
        self.play(Create(curve, run_time=2))
        
        self.wait(3)
        self.play(FadeOut(VGroup(title, complexity_info, axes, curve, chart_title, x_label, y_label)))

    def conclusion_sequence(self):
        # Final summary
        conclusion_title = Text(
            "The Future is Computational",
            font_size=48,
            color=GOLD,
            font="Consolas",
            weight=BOLD
        ).to_edge(UP)
        
        # Key takeaways with elegant animations
        takeaways = VGroup(
            Text("🚀 Market growing at 15% annually", font_size=28, color=WHITE),
            Text("💡 AI adoption accelerating rapidly", font_size=28, color=BLUE_B),
            Text("⚡ Dramatic speed & cost improvements", font_size=28, color=GREEN_B),
            Text("🎯 Tackling previously impossible targets", font_size=28, color=PURPLE_B),
        ).arrange(DOWN, buff=0.8).center().shift(UP*0.5)
        
        # Final message
        final_message = Text(
            "Computational drug discovery is not just the future,\nit's happening now.",
            font_size=32,
            color=YELLOW,
            font="Consolas",
            weight=BOLD
        ).next_to(takeaways, DOWN, buff=1)
        
        self.play(Write(conclusion_title, run_time=1.5))
        self.play(LaggedStart(*[Write(takeaway) for takeaway in takeaways], lag_ratio=0.5))
        self.play(Write(final_message, run_time=1.5))
        
        self.wait(4)

  